#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;

    // Create a simple list template
    CPListItem *alertsItem = [[CPListItem alloc] initWithText:@"ChillBaby Monitor"
                                                   detailText:@"Baby monitoring dashboard"];

    CPListItem *statusItem = [[CPListItem alloc] initWithText:@"Device Status"
                                                    detailText:@"Check connected devices"];

    CPListSection *section = [[CPListSection alloc] initWithItems:@[alertsItem, statusItem]];

    CPListTemplate *listTemplate = [[CPListTemplate alloc] initWithTitle:@"ChillBaby"
                                                                 sections:@[section]];

    // Set the root template
    [self.interfaceController setRootTemplate:listTemplate animated:YES completion:^(BOOL templateSuccess, NSError * _Nullable templateError) {
        if (templateSuccess) {
            NSLog(@"CarPlay root template set successfully");
        } else {
            NSLog(@"Failed to set CarPlay root template: %@", templateError.localizedDescription);
        }
    }];

    // Notify React Native that CarPlay is connected
    [self notifyReactNativeCarPlayConnected:YES];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene 
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");
    
    self.interfaceController = nil;
    
    // Notify React Native that CarPlay is disconnected
    [self notifyReactNativeCarPlayConnected:NO];
}

- (void)notifyReactNativeCarPlayConnected:(BOOL)connected {
    // This method will notify React Native about CarPlay connection status
    // The react-native-carplay library should handle this automatically
    NSLog(@"CarPlay connection status: %@", connected ? @"Connected" : @"Disconnected");
}

@end
