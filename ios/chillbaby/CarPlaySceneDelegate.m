#import "CarPlaySceneDelegate.h"
#import <RNCarPlay.h>
#import <UIKit/UIKit.h>
#import <React/RCTBridge.h>
#import "CarPlayEventEmitter.h"
#import "AppDelegate.h"

@implementation CarPlaySceneDelegate

#pragma mark - CPTemplateApplicationSceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
      didConnectInterfaceController:(CPInterfaceController *)interfaceController
{
  NSLog(@"[CarPlay] Connected, attaching RNCarPlay…");

  // Connect RNCarPlay with the interface controller + CarPlay window
  [RNCarPlay connectWithInterfaceController:interfaceController
                                     window:templateApplicationScene.carWindow];

  // Send foreground event when CarPlay connects
  [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];

  // Create a basic template to ensure CarPlay is visible
  [self setupInitialTemplate:interfaceController];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
   didDisconnectInterfaceController:(CPInterfaceController *)interfaceController
{
  NSLog(@"[CarPlay] Disconnected, cleaning up…");

  // Send background event when CarPlay disconnects
  [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];

  // Disconnect RNCarPlay
  [RNCarPlay disconnect];
}

#pragma mark - UIScene Lifecycle

- (void)sceneWillEnterForeground:(UIScene *)scene {
  NSLog(@"[CarPlay] Scene entering foreground");
  [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];
}

- (void)sceneDidEnterBackground:(UIScene *)scene {
  NSLog(@"[CarPlay] Scene entered background");
  [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];
}

#pragma mark - Setup Methods

- (void)setupInitialTemplate:(CPInterfaceController *)interfaceController {
  // Create a simple list template to ensure CarPlay is visible
  CPListItem *alertsItem = [[CPListItem alloc] initWithText:@"ChillBaby Monitor"
                                                 detailText:@"Baby monitoring dashboard"];

  CPListItem *statusItem = [[CPListItem alloc] initWithText:@"Device Status"
                                                  detailText:@"Check connected devices"];

  CPListSection *section = [[CPListSection alloc] initWithItems:@[alertsItem, statusItem]];

  CPListTemplate *listTemplate = [[CPListTemplate alloc] initWithTitle:@"ChillBaby"
                                                               sections:@[section]];

  // Set the root template
  [interfaceController setRootTemplate:listTemplate animated:YES completion:^(BOOL success, NSError * _Nullable error) {
    if (success) {
      NSLog(@"[CarPlay] Root template set successfully");
    } else {
      NSLog(@"[CarPlay] Failed to set root template: %@", error.localizedDescription);
    }
  }];
}

@end
