#import "CarPlaySceneDelegate.h"
#import <RNCarPlay.h>
#import <UIKit/UIKit.h>

@implementation CarPlaySceneDelegate

#pragma mark - CPTemplateApplicationSceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
      didConnectInterfaceController:(CPInterfaceController *)interfaceController
{
  NSLog(@"[CarPlay] Connected, attaching RNCarP<PERSON>…");

  // Connect RNCarPlay with the interface controller + CarPlay window
  [RNCarPlay connectWithInterfaceController:interfaceController
                                     window:templateApplicationScene.carWindow];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
   didDisconnectInterfaceController:(CPInterfaceController *)interfaceController
{
  NSLog(@"[CarPlay] Disconnected, cleaning up…");

  // Disconnect RNCarPlay
  [RNCarPlay disconnect];
}

#pragma mark - UIScene Lifecycle

- (void)sceneWillEnterForeground:(UIScene *)scene {
  NSLog(@"[CarPlay] Scene entering foreground");
}

- (void)sceneDidEnterBackground:(UIScene *)scene {
  NSLog(@"[CarPlay] Scene entered background");
}

@end
