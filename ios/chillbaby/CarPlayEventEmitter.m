#import "CarPlayEventEmitter.h"
#import <React/RCTLog.h>

@implementation CarPlayEventEmitter

static CarPlayEventEmitter *sharedInstance = nil;

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[@"carPlayForeground", @"carPlayBackground"];
}

- (void)sendCarPlayForegroundEvent {
    [self sendEventWithName:@"carPlayForeground" body:@{}];
    RCTLogInfo(@"CarPlay foreground event sent");
}

- (void)sendCarPlayBackgroundEvent {
    [self sendEventWithName:@"carPlayBackground" body:@{}];
    RCTLogInfo(@"CarPlay background event sent");
}

@end
