#import "MaxRCTCarPlayNotificationManager.h"
#import <React/RCTLog.h>
#import <React/RCTUtils.h>

@implementation MaxRCTCarPlayNotificationManager

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[@"carPlayNotification", @"carPlayConnected", @"carPlayDisconnected"];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // Initialize any properties here
        RCTLogInfo(@"MaxRCTCarPlayNotificationManager initialized");
    }
    return self;
}

RCT_EXPORT_METHOD(sendCarPlayNotification:(NSString *)title 
                  message:(NSString *)message
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @try {
            RCTLogInfo(@"Attempting to send CarPlay notification: %@ - %@", title, message);
            
            // Check if we have an interface controller
            if (!self.interfaceController) {
                RCTLogWarn(@"No CarPlay interface controller available");
                resolve(@{@"success": @NO, @"message": @"No CarPlay interface controller"});
                return;
            }
            
            // Create a banner notification
            if (@available(iOS 14.0, *)) {
                // For iOS 14+, we can use the newer banner notification API
                [self showBannerNotification:title message:message resolve:resolve];
            } else {
                // For older iOS versions, fall back to alert template
                [self showAlertTemplate:title message:message resolve:resolve];
            }
            
        } @catch (NSException *exception) {
            RCTLogError(@"Error sending CarPlay notification: %@", exception.reason);
            reject(@"CARPLAY_ERROR", exception.reason, nil);
        }
    });
}

- (void)showBannerNotification:(NSString *)title 
                       message:(NSString *)message 
                       resolve:(RCTPromiseResolveBlock)resolve API_AVAILABLE(ios(14.0)) {
    
    // Create a simple alert template for banner-like notification
    CPAlertTemplate *alertTemplate = [[CPAlertTemplate alloc] initWithTitleVariants:@[title ?: @"Notification"]
                                                                             actions:@[
        [[CPAlertAction alloc] initWithTitle:@"OK" style:CPAlertActionStyleDefault handler:^(CPAlertAction * _Nonnull action) {
            [self.interfaceController dismissTemplateAnimated:YES completion:^(BOOL success, NSError * _Nullable error) {
                if (error) {
                    RCTLogError(@"Error dismissing CarPlay alert: %@", error.localizedDescription);
                }
            }];
        }]
    ]];
    
    // Set the message if provided
    if (message && message.length > 0) {
        // For iOS 14+, we can set additional text
        // Note: CPAlertTemplate doesn't have a direct message property, so we'll include it in the title
        NSString *fullTitle = [NSString stringWithFormat:@"%@\n%@", title ?: @"Notification", message];
        alertTemplate = [[CPAlertTemplate alloc] initWithTitleVariants:@[fullTitle]
                                                               actions:alertTemplate.actions];
    }
    
    [self.interfaceController presentTemplate:alertTemplate animated:YES completion:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            RCTLogInfo(@"CarPlay notification presented successfully");
            resolve(@{@"success": @YES, @"message": @"Notification sent successfully"});
        } else {
            RCTLogError(@"Failed to present CarPlay notification: %@", error.localizedDescription);
            resolve(@{@"success": @NO, @"message": error.localizedDescription ?: @"Unknown error"});
        }
    }];
}

- (void)showAlertTemplate:(NSString *)title 
                  message:(NSString *)message 
                  resolve:(RCTPromiseResolveBlock)resolve {
    
    NSString *fullMessage = message && message.length > 0 ? 
        [NSString stringWithFormat:@"%@\n%@", title ?: @"Notification", message] : 
        (title ?: @"Notification");
    
    CPAlertTemplate *alertTemplate = [[CPAlertTemplate alloc] initWithTitleVariants:@[fullMessage]
                                                                             actions:@[
        [[CPAlertAction alloc] initWithTitle:@"OK" style:CPAlertActionStyleDefault handler:^(CPAlertAction * _Nonnull action) {
            [self.interfaceController dismissTemplateAnimated:YES completion:nil];
        }]
    ]];
    
    [self.interfaceController presentTemplate:alertTemplate animated:YES completion:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            RCTLogInfo(@"CarPlay alert presented successfully");
            resolve(@{@"success": @YES, @"message": @"Alert sent successfully"});
        } else {
            RCTLogError(@"Failed to present CarPlay alert: %@", error.localizedDescription);
            resolve(@{@"success": @NO, @"message": error.localizedDescription ?: @"Unknown error"});
        }
    }];
}

// Method to set the interface controller (called from CarPlaySceneDelegate)
- (void)setInterfaceController:(CPInterfaceController *)interfaceController {
    self.interfaceController = interfaceController;
    RCTLogInfo(@"CarPlay interface controller set in notification manager");
    
    // Emit event that CarPlay is connected
    [self sendEventWithName:@"carPlayConnected" body:@{@"connected": @YES}];
}

// Method to clear the interface controller
- (void)clearInterfaceController {
    self.interfaceController = nil;
    RCTLogInfo(@"CarPlay interface controller cleared from notification manager");
    
    // Emit event that CarPlay is disconnected
    [self sendEventWithName:@"carPlayDisconnected" body:@{@"connected": @NO}];
}

// Method to check if CarPlay is available
RCT_EXPORT_METHOD(isCarPlayAvailable:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL available = self.interfaceController != nil;
    resolve(@{@"available": @(available)});
}

// Method to get CarPlay connection status
RCT_EXPORT_METHOD(getConnectionStatus:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL connected = self.interfaceController != nil;
    resolve(@{@"connected": @(connected)});
}

@end
