/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  Image,
  NativeEventEmitter,
  NativeModules,
  Text,
  View,
} from 'react-native';
import {
  CarPlay,
  GridTemplate,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
  ListTemplate,
} from 'react-native-carplay';
import { useSelector } from 'react-redux';
import messaging from '@react-native-firebase/messaging';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayComponent = props => {
  const { navigation } = props;
  const user = useSelector(state => state.auth.userData);
  const { alertData, activeChildDetail } = useSelector(
    state => state.bluetooth,
  );
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);
  const [carPlayForeground, setCarPlayForeground] = useState(false);
  const carPlayForegroundRef = useRef(carPlayForeground);

  useEffect(() => {
    carPlayForegroundRef.current = carPlayForeground;
  }, [carPlayForeground]);

  // Add this useEffect right after the component initialization
  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected');

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn('⚠️ State mismatch detected!');
      console.log('  - Native says:', CarPlay.connected);
      console.log('  - State says:', carPlayConnected);
    }
  }, [carPlayConnected]);

  // Handle notification function
  const handleNotification = useCallback(
    notification => {
      if (carPlayConnected) {
        showNotification(notification);
      } else {
        pendingNotification.current = notification;
      }
    },
    [carPlayConnected, showNotification],
  );

  // State for CarPlay service
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [notificationQueue, setNotificationQueue] = useState([]);
  const [alertQueue, setAlertQueue] = useState([]);

  /**
   * Set up the initial CarPlay template
   */
  const setupInitialTemplate = useCallback(() => {
    try {
      // Check if CarPlay is actually connected before setting template
      if (!carPlayConnected) {
        console.log('CarPlay not connected, skipping template setup');
        return;
      }

      const template = {
        type: 'list',
        title: 'ChillBaby',
        sections: [
          {
            header: 'Baby Monitor',
            items: [
              {
                text: 'Recent Alerts',
                detailText: 'View recent baby monitoring alerts',
                onPress: () => showRecentAlerts(),
              },
              {
                text: 'Device Status',
                detailText: 'Check connected device status',
                onPress: () => showDeviceStatus(),
              },
            ],
          },
        ],
      };

      CarPlay.setRootTemplate(template);
      setCurrentTemplate(template);
      console.log('CarPlay template set successfully');
    } catch (error) {
      console.error('Error setting up CarPlay template:', error);
      // Don't throw error, just log it
    }
  }, [carPlayConnected]);

  /**
   * Show a notification on CarPlay
   * @param {Object} notification - Notification object
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {string} notification.type - Notification type (info, warning, critical)
   * @param {Function} notification.onPress - Optional callback when notification is pressed
   */
  const showNotification = useCallback(
    notification => {
      if (!carPlayConnected) {
        // Queue notification for when CarPlay connects
        setNotificationQueue(prev => [...prev, notification]);
        console.log(
          'CarPlay not connected, queuing notification:',
          notification.title,
        );
        return;
      }

      try {
        const { title, message, type = 'info', onPress } = notification;

        // Create CarPlay notification
        const carPlayNotification = {
          title: title || 'ChillBaby Alert',
          subtitle: message,
          informativeText: getNotificationTypeText(type),
          onPress: onPress || (() => showNotificationDetails(notification)),
        };

        // Show banner notification
        CarPlay.showBannerNotification(carPlayNotification);

        console.log('CarPlay notification shown:', title);
      } catch (error) {
        console.error('Error showing CarPlay notification:', error);
        // Don't crash the app, just log the error
      }
    },
    [carPlayConnected],
  );

  /**
   * Show an alert dialog on CarPlay
   * @param {Object} alert - Alert object
   * @param {string} alert.title - Alert title
   * @param {string} alert.message - Alert message
   * @param {Array} alert.actions - Array of action objects
   */
  const showAlert = useCallback(
    alert => {
      if (!carPlayConnected) {
        // Queue alert for when CarPlay connects
        setAlertQueue(prev => [...prev, alert]);
        console.log('CarPlay not connected, queuing alert:', alert.title);
        return;
      }

      try {
        const { title, message, actions = [] } = alert;

        // Create CarPlay alert template
        const alertTemplate = {
          type: 'alert',
          title: title || 'ChillBaby Alert',
          subtitle: message,
          actions: [
            ...actions.map(action => ({
              title: action.title,
              style: action.style || 'default',
              onPress: action.onPress || (() => {}),
            })),
            {
              title: 'Dismiss',
              style: 'cancel',
              onPress: () => dismissAlert(),
            },
          ],
        };

        CarPlay.presentTemplate(alertTemplate);
        console.log('CarPlay alert shown:', title);
      } catch (error) {
        console.error('Error showing CarPlay alert:', error);
        // Don't crash the app, just log the error
      }
    },
    [carPlayConnected],
  );

  /**
   * Show critical baby monitoring alert
   * @param {Object} babyAlert - Baby monitoring alert data
   */
  const showBabyAlert = useCallback(
    babyAlert => {
      const { type, temperature, humidity, timestamp } = babyAlert;

      let title = 'Baby Monitor Alert';
      let message = '';
      let actions = [];

      switch (type) {
        case 'temperature_high':
          title = '🌡️ High Temperature Alert';
          message = `Temperature: ${temperature}°C - Check baby immediately`;
          actions = [
            {
              title: 'Call Emergency',
              style: 'destructive',
              onPress: () => callEmergency(),
            },
            {
              title: 'View Details',
              onPress: () => showTemperatureDetails(babyAlert),
            },
          ];
          break;

        case 'temperature_low':
          title = '❄️ Low Temperature Alert';
          message = `Temperature: ${temperature}°C - Baby may be cold`;
          actions = [
            {
              title: 'View Details',
              onPress: () => showTemperatureDetails(babyAlert),
            },
          ];
          break;

        case 'humidity_alert':
          title = '💧 Humidity Alert';
          message = `Humidity: ${humidity}% - Check room conditions`;
          actions = [
            {
              title: 'View Details',
              onPress: () => showHumidityDetails(babyAlert),
            },
          ];
          break;

        case 'device_disconnected':
          title = '📱 Device Disconnected';
          message = 'Baby monitor lost connection - Check device';
          actions = [
            {
              title: 'Reconnect',
              onPress: () => attemptReconnection(),
            },
          ];
          break;

        default:
          message = 'Check baby monitoring app for details';
      }

      // Show as both notification and alert for critical alerts
      showNotification({
        title,
        message,
        type: 'critical',
      });

      showAlert({
        title,
        message,
        actions,
      });
    },
    [showNotification, showAlert],
  );

  /**
   * Process queued notifications when CarPlay connects
   */
  const processQueuedNotifications = useCallback(() => {
    // Process queued notifications
    setNotificationQueue(prevQueue => {
      prevQueue.forEach(notification => {
        showNotification(notification);
      });
      return [];
    });

    // Process queued alerts
    setAlertQueue(prevQueue => {
      prevQueue.forEach(alert => {
        showAlert(alert);
      });
      return [];
    });
  }, [showNotification, showAlert]);

  /**
   * Get notification type text for display
   */
  const getNotificationTypeText = useCallback(type => {
    switch (type) {
      case 'critical':
        return '🚨 Critical Alert';
      case 'warning':
        return '⚠️ Warning';
      case 'info':
      default:
        return 'ℹ️ Information';
    }
  }, []);

  /**
   * Show recent alerts in CarPlay
   */
  const showRecentAlerts = useCallback(() => {
    // This would integrate with your existing alert system
    console.log('Showing recent alerts in CarPlay');
    // Implementation would fetch recent alerts and display them
  }, []);

  /**
   * Show device status in CarPlay
   */
  const showDeviceStatus = useCallback(() => {
    // This would integrate with your existing device status system
    console.log('Showing device status in CarPlay');
    // Implementation would fetch device status and display it
  }, []);

  /**
   * Show notification details
   */
  const showNotificationDetails = useCallback(notification => {
    console.log('Showing notification details:', notification);
    // Implementation would show detailed view of notification
  }, []);

  /**
   * Show temperature details
   */
  const showTemperatureDetails = useCallback(alert => {
    console.log('Showing temperature details:', alert);
    // Implementation would show temperature trend and details
  }, []);

  /**
   * Show humidity details
   */
  const showHumidityDetails = useCallback(alert => {
    console.log('Showing humidity details:', alert);
    // Implementation would show humidity trend and details
  }, []);

  /**
   * Call emergency services
   */
  const callEmergency = useCallback(() => {
    console.log('Emergency call initiated from CarPlay');
    // Implementation would initiate emergency call
  }, []);

  /**
   * Attempt device reconnection
   */
  const attemptReconnection = useCallback(() => {
    console.log('Attempting device reconnection from CarPlay');
    // Implementation would attempt to reconnect to baby monitor
  }, []);

  /**
   * Dismiss current alert
   */
  const dismissAlert = useCallback(() => {
    try {
      CarPlay.dismissTemplate();
    } catch (error) {
      console.error('Error dismissing CarPlay alert:', error);
    }
  }, []);

  /**
   * Check if CarPlay is connected
   */
  const isCarPlayConnected = useCallback(() => {
    return carPlayConnected;
  }, [carPlayConnected]);

  /**
   * Cleanup CarPlay service
   */
  const cleanup = useCallback(() => {
    try {
      if (carPlayConnected) {
        CarPlay.disconnect();
      }
      setCarPlayConnected(false);
      setCurrentTemplate(null);
      setNotificationQueue([]);
      setAlertQueue([]);
    } catch (error) {
      console.error('Error cleaning up CarPlay service:', error);
    }
  }, [carPlayConnected]);

  // Handle notification when alertData changes
  useEffect(() => {
    if (alertData && carPlayConnected) {
      showBabyAlert(alertData);
    }
  }, [alertData, carPlayConnected, showBabyAlert]);

  // Process queued notifications when CarPlay connects
  useEffect(() => {
    if (carPlayConnected) {
      processQueuedNotifications();
      setupInitialTemplate();
    }
  }, [carPlayConnected, processQueuedNotifications, setupInitialTemplate]);

  // Return null as this is a service component
  return null;
};

export default CarPlayComponent;
