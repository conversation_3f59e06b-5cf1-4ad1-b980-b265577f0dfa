/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Image,
  NativeEventEmitter,
  NativeModules,
  Text,
  View,
} from "react-native";
import {
  CarPlay,
  GridTemplate,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
  ListTemplate,
} from "react-native-carplay";
import { useSelector } from "react-redux";
import messaging from "@react-native-firebase/messaging";
import { sendErrorReport } from "../utils/commonFunction";

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    "Available modules:",
    Object.keys(NativeModules).filter((key) => key.includes("CarPlay"))
  );
}

const CarPlayComponent = (props) => {
  const { navigation } = props;
  const user = useSelector((state) => state.auth.userData);
  const { alertData, activeChildDetail } = useSelector(
    (state) => state.bluetooth
  );
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);
  const [carPlayForeground, setCarPlayForeground] = useState(false);
  const carPlayForegroundRef = useRef(carPlayForeground);

  useEffect(() => {
    carPlayForegroundRef.current = carPlayForeground;
  }, [carPlayForeground]);

  // Add this useEffect right after the component initialization
  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, "carplay_connected");

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn("⚠️ State mismatch detected!");
      console.log("  - Native says:", CarPlay.connected);
      console.log("  - State says:", carPlayConnected);
    }
  }, [carPlayConnected]);

  /**
   * Set up the initial CarPlay template
   */
  setupInitialTemplate() {
    try {
      // Check if CarPlay is actually connected before setting template
      if (!this.isConnected) {
        console.log('CarPlay not connected, skipping template setup');
        return;
      }

      const template = {
        type: 'list',
        title: 'ChillBaby',
        sections: [
          {
            header: 'Baby Monitor',
            items: [
              {
                text: 'Recent Alerts',
                detailText: 'View recent baby monitoring alerts',
                onPress: () => this.showRecentAlerts(),
              },
              {
                text: 'Device Status',
                detailText: 'Check connected device status',
                onPress: () => this.showDeviceStatus(),
              },
            ],
          },
        ],
      };

      CarPlay.setRootTemplate(template);
      this.currentTemplate = template;
      console.log('CarPlay template set successfully');
    } catch (error) {
      console.error('Error setting up CarPlay template:', error);
      // Don't throw error, just log it
    }
  }

  /**
   * Show a notification on CarPlay
   * @param {Object} notification - Notification object
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {string} notification.type - Notification type (info, warning, critical)
   * @param {Function} notification.onPress - Optional callback when notification is pressed
   */
  showNotification(notification) {
    if (!this.isConnected) {
      // Queue notification for when CarPlay connects
      this.notificationQueue.push(notification);
      console.log(
        'CarPlay not connected, queuing notification:',
        notification.title,
      );
      return;
    }

    try {
      const { title, message, type = 'info', onPress } = notification;

      // Create CarPlay notification
      const carPlayNotification = {
        title: title || 'ChillBaby Alert',
        subtitle: message,
        informativeText: this.getNotificationTypeText(type),
        onPress: onPress || (() => this.showNotificationDetails(notification)),
      };

      // Show banner notification
      CarPlay.showBannerNotification(carPlayNotification);

      console.log('CarPlay notification shown:', title);
    } catch (error) {
      console.error('Error showing CarPlay notification:', error);
      // Don't crash the app, just log the error
    }
  }

  /**
   * Show an alert dialog on CarPlay
   * @param {Object} alert - Alert object
   * @param {string} alert.title - Alert title
   * @param {string} alert.message - Alert message
   * @param {Array} alert.actions - Array of action objects
   */
  showAlert(alert) {
    if (!this.isConnected) {
      // Queue alert for when CarPlay connects
      this.alertQueue.push(alert);
      console.log('CarPlay not connected, queuing alert:', alert.title);
      return;
    }

    try {
      const { title, message, actions = [] } = alert;

      // Create CarPlay alert template
      const alertTemplate = {
        type: 'alert',
        title: title || 'ChillBaby Alert',
        subtitle: message,
        actions: [
          ...actions.map(action => ({
            title: action.title,
            style: action.style || 'default',
            onPress: action.onPress || (() => {}),
          })),
          {
            title: 'Dismiss',
            style: 'cancel',
            onPress: () => this.dismissAlert(),
          },
        ],
      };

      CarPlay.presentTemplate(alertTemplate);
      console.log('CarPlay alert shown:', title);
    } catch (error) {
      console.error('Error showing CarPlay alert:', error);
      // Don't crash the app, just log the error
    }
  }

  /**
   * Show critical baby monitoring alert
   * @param {Object} babyAlert - Baby monitoring alert data
   */
  showBabyAlert(babyAlert) {
    const { type, temperature, humidity, timestamp } = babyAlert;

    let title = 'Baby Monitor Alert';
    let message = '';
    let actions = [];

    switch (type) {
      case 'temperature_high':
        title = '🌡️ High Temperature Alert';
        message = `Temperature: ${temperature}°C - Check baby immediately`;
        actions = [
          {
            title: 'Call Emergency',
            style: 'destructive',
            onPress: () => this.callEmergency(),
          },
          {
            title: 'View Details',
            onPress: () => this.showTemperatureDetails(babyAlert),
          },
        ];
        break;

      case 'temperature_low':
        title = '❄️ Low Temperature Alert';
        message = `Temperature: ${temperature}°C - Baby may be cold`;
        actions = [
          {
            title: 'View Details',
            onPress: () => this.showTemperatureDetails(babyAlert),
          },
        ];
        break;

      case 'humidity_alert':
        title = '💧 Humidity Alert';
        message = `Humidity: ${humidity}% - Check room conditions`;
        actions = [
          {
            title: 'View Details',
            onPress: () => this.showHumidityDetails(babyAlert),
          },
        ];
        break;

      case 'device_disconnected':
        title = '📱 Device Disconnected';
        message = 'Baby monitor lost connection - Check device';
        actions = [
          {
            title: 'Reconnect',
            onPress: () => this.attemptReconnection(),
          },
        ];
        break;

      default:
        message = 'Check baby monitoring app for details';
    }

    // Show as both notification and alert for critical alerts
    this.showNotification({
      title,
      message,
      type: 'critical',
    });

    this.showAlert({
      title,
      message,
      actions,
    });
  }

  /**
   * Process queued notifications when CarPlay connects
   */
  processQueuedNotifications() {
    // Process queued notifications
    while (this.notificationQueue.length > 0) {
      const notification = this.notificationQueue.shift();
      this.showNotification(notification);
    }

    // Process queued alerts
    while (this.alertQueue.length > 0) {
      const alert = this.alertQueue.shift();
      this.showAlert(alert);
    }
  }

  /**
   * Get notification type text for display
   */
  getNotificationTypeText(type) {
    switch (type) {
      case 'critical':
        return '🚨 Critical Alert';
      case 'warning':
        return '⚠️ Warning';
      case 'info':
      default:
        return 'ℹ️ Information';
    }
  }

  /**
   * Show recent alerts in CarPlay
   */
  showRecentAlerts() {
    // This would integrate with your existing alert system
    console.log('Showing recent alerts in CarPlay');
    // Implementation would fetch recent alerts and display them
  }

  /**
   * Show device status in CarPlay
   */
  showDeviceStatus() {
    // This would integrate with your existing device status system
    console.log('Showing device status in CarPlay');
    // Implementation would fetch device status and display it
  }

  /**
   * Show notification details
   */
  showNotificationDetails(notification) {
    console.log('Showing notification details:', notification);
    // Implementation would show detailed view of notification
  }

  /**
   * Show temperature details
   */
  showTemperatureDetails(alert) {
    console.log('Showing temperature details:', alert);
    // Implementation would show temperature trend and details
  }

  /**
   * Show humidity details
   */
  showHumidityDetails(alert) {
    console.log('Showing humidity details:', alert);
    // Implementation would show humidity trend and details
  }

  /**
   * Call emergency services
   */
  callEmergency() {
    console.log('Emergency call initiated from CarPlay');
    // Implementation would initiate emergency call
  }

  /**
   * Attempt device reconnection
   */
  attemptReconnection() {
    console.log('Attempting device reconnection from CarPlay');
    // Implementation would attempt to reconnect to baby monitor
  }

  /**
   * Dismiss current alert
   */
  dismissAlert() {
    try {
      CarPlay.dismissTemplate();
    } catch (error) {
      console.error('Error dismissing CarPlay alert:', error);
    }
  }

  /**
   * Check if CarPlay is connected
   */
  isCarPlayConnected() {
    return this.isConnected;
  }

  /**
   * Cleanup CarPlay service
   */
  cleanup() {
    try {
      if (this.isConnected) {
        CarPlay.disconnect();
      }
      this.isConnected = false;
      this.currentTemplate = null;
      this.notificationQueue = [];
      this.alertQueue = [];
    } catch (error) {
      console.error('Error cleaning up CarPlay service:', error);
    }
  }
}

// Export singleton instance
export default new CarPlayService();
