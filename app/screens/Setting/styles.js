import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';
import { store } from '../../redux/store/configureStore';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor
  },
  flatListView: {
    backgroundColor: BaseColor.whiteColor,
    marginHorizontal: 16,
    marginVertical: 30,
    borderRadius: 16,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingVertical: 26,
    // borderBottomColor: BaseColor.textGrey,
    // borderBottomWidth: 0.5
  },
  settingName: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tempC: {
    width: 32,
    height: 32,
    backgroundColor: '#dd2c00',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tempTxt: {
    textAlign: 'center',
    textAlignVertical: 'center',
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
  },
  settingIcon: {
    width: 40,
    height: 40,
    backgroundColor: BaseColor.whiteColor,
    textAlignVertical: 'center',
    textAlign: 'center',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5
  },
  infoText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    paddingHorizontal: 16,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
  },
  securityCheckView: {
    width: 50,
    alignSelf: 'center',
  },
  aboutText: {
    fontSize: 16,
    color: BaseColor.whiteColor,
    paddingBottom: 7,
    fontFamily: FontFamily.default,
    // fontWeight: 'bold',
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',
    bottom: 0,
    // top: 0,
    alignSelf: 'flex-start',
    borderRadius: 16,
    padding: 12,
    margin: 25,

  },
  flagDesign: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 140,
    padding: 5
  },

});

export default styles;
