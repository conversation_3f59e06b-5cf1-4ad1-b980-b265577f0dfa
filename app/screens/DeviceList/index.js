/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
} from 'react-native';
// import QRCodeScanner from "react-native-qrcode-scanner";
import _, {
  find,
  findIndex,
  isArray,
  isBoolean,
  isEmpty,
  isObject,
  isUndefined,
} from 'lodash';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import { useDispatch, useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';
import { openSettings } from 'react-native-permissions';
import _BackgroundTimer from 'react-native-background-timer';
import { Button } from 'react-native-share';
import { CustomIcon } from '../../config/LoadIcons';
import styles from './styles';
import CHeader from '../../components/CHeader';
import GradientBack from '../../components/gradientBack';
import { translate } from '../../lang/Translate';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import { sendErrorReport } from '../../utils/commonFunction';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';
import StepPopup from '../../components/StepPopup';
import AuthAction from '../../redux/reducers/auth/actions';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

/**
 *
 *@module bleList
 *
 */
const BleList = ({ navigation, route }) => {
  const fromChildInfo = route?.params?.fromChildInfo;
  // console.log('from child----1', fromChildInfo);
  const dispatch = useDispatch();
  const token = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [isScanning, setIsScanning] = useState(false);
  const peripherals = new Map();
  const [list, setList] = useState([]);
  const connectedDeviceDetail = useSelector(
    state => state.bluetooth.connectedDeviceDetail,
  );

  // console.log('🚀 ~ BleList ~ connectedDeviceDetail:', connectedDeviceDetail);
  const { isClickAddQr, bleDeviceList, isSkipShow, isBleConnected } =
    useSelector(state => state.bluetooth);
  const {
    setStep5Done,
    setStep5bDone,
    setStep5cDone,
    setStep5dDone,
    setStep5eDone,
    setStep5fDone,
  } = AuthAction;
  const {
    step5Done,
    step5bDone,
    step5cDone,
    step5dDone,
    step5eDone,
    step5fDone,
    closeOnboarding,
  } = useSelector(state => state.auth);
  // const [connnectedID, setconnnectedID] = useState("");
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [skip, setSkip] = useState(false);
  const [scanningText, setScanningText] = useState(
    translate('searchingQRCode'),
  );
  const [showStep5, setShowStep5] = useState(false);
  const [showStep5b, setShowStep5b] = useState(false);
  const [showStep5c, setShowStep5c] = useState(false);
  const [showStep5d, setShowStep5d] = useState(false);
  const [showStep5e, setShowStep5e] = useState(false);
  const [showStep5f, setShowStep5f] = useState(false);
  const scanner = useRef(null);
  const [spin, setSpin] = useState(false);
  const userData = useSelector(state => state.auth.userData);

  // const [bluetoothStatus, setbluetoothStatus] = useState(false);

  useEffect(() => {
    if (!step5Done && !closeOnboarding) {
      setShowStep5(true);
    }
    // else if (
    //   step5Done &&
    //   (!step5bDone || !step5cDone || !step5dDone || !step5eDone || !step5fDone)
    // ) {
    //   setShowStep5(true);
    // }
  }, []);

  const startScan = () => {
    if (!isScanning) {
      BleManager.scan([], 3, true)
        .then(results => {
          setIsScanning(true);
          setrefresh(false);
        })
        .catch(err => {
          setrefresh(false);
          console.error(err);
          sendErrorReport(err, 'scan_error');
        });
    }
  };

  useEffect(() => {
    // const found = bleDeviceList.find((obj) => obj.name.includes("Baby_Auto"));
    // if (!found) {
    //   onRefresh();
    //   // startScan();
    // }
    // setTimeout(() => {
    //   startScan();
    // }, 1500);

    onRefresh();
  }, []);

  // useEffect(() => {
  //   dispatch(BluetoothAction.setBleDeviceList(list));
  // }, [list]);

  const handleStopScan = () => {
    setIsScanning(false);
    setisRefreshing(false);
    dispatch(BluetoothAction.setClickAddQr(false));
  };

  const handleDiscoverPeripheral = peripheral => {
    if (!peripheral.name) {
      peripheral.name = 'NO NAME';
    }
    peripherals.set(peripheral.id, peripheral);
    setList(Array.from(peripherals.values()));
  };

  BluetoothStateManager.onStateChange(bluetoothState => {
    // do something...
    switch (bluetoothState) {
      case 'Unknown':
      case 'Resetting':
      case 'Unsupported':
      case 'Unauthorized':
      case 'PoweredOff':
        Toast.show(translate('turnOnBle'));
      // Toast.show("Please turn on your bluetooth");
      case 'PoweredOn':
        // startScan();
        break;
      default:
        break;
    }
  }, true /*= emitCurrentState */);

  useEffect(() => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          // Toast.show("Please turn on your bluetooth");
          Toast.show(translate('turnOnBle'));
          if (Platform.OS == 'android') {
            sendErrorReport(true, 'requestToEnableDevi');
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
              console.log('BLUETOOTH_CONNECT---', res);
              if (res === 'granted') {
                BluetoothStateManager.requestToEnable().then(result => {
                  console.log(
                    'BluetoothStateManager.requestToEnable -> result',
                    result,
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then(result => {
                console.log('BLUETOOTH_CONNECT----1', result);
              })
              .then(statuses => {
                console.log(
                  'BLUETOOTH_CONNECT--2',
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                );
              });
          } else {
            console.log('=====2222222');
            BluetoothStateManager.openSettings();
          }
          break;
        case 'PoweredOn':
          // startScan();
          break;
        default:
          break;
      }
    });
  }, []);

  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    // if (isClickAddQr || isRefreshing) {
    let discoverPeripheralListener,
      stopScanListener,
      centralManagerRestoreState;
    console.log('event emitter ble manager--------function called');
    if (Platform.OS === 'ios') {
      centralManagerRestoreState = bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );
    }

    discoverPeripheralListener = bleManagerEmitter.addListener(
      'BleManagerDiscoverPeripheral',
      handleDiscoverPeripheral,
    );
    stopScanListener = bleManagerEmitter.addListener(
      'BleManagerStopScan',
      handleStopScan,
    );

    if (Platform.OS === 'android' && Platform.Version >= 23) {
      console.log('called---5');
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      ).then(result => {
        if (result) {
          console.log('Permission is OK');
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          ).then(res => {
            if (res) {
              console.log('User accept');
            } else {
              console.log('User refuse');
            }
          });
        }
      });
    }
    // }

    return () => {
      console.log('unmount');
      // bleManagerEmitter.removeListener(
      //   'BleManagerDiscoverPeripheral',
      //   handleDiscoverPeripheral
      // );
      // bleManagerEmitter.removeListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.removeListener(
      //   'BleManagerDisconnectPeripheral',
      //   handleDisconnectedPeripheral
      // );
      // bleManagerEmitter.removeListener(
      //   'BleManagerDidUpdateValueForCharacteristic',
      //   handleUpdateValueForCharacteristic
      // );
      centralManagerRestoreState?.remove();
      stopScanListener?.remove();
      discoverPeripheralListener?.remove();
    };
  }, []);

  function handleBackButtonClick() {
    navigation.navigate(translate('home'));
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for onReadQRList
   * @function onReadQRList
   * @param {object} data device_bluetooth_name
   */

  const onReadQRList = async item => {
    // sendErrorReport(item, 'item__item');
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    const testUser =
      !isEmpty(userData) && userData.email === '<EMAIL>';
    console.log('🚀 ~ onReadQRList ~ testUser:', testUser);

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        'POST',
        {
          device_ssid: testUser ? `${item?.name}AVDF` : item?.name, //remove this string hardcoded
          product_id: item?.id,
          lang_code: languageData?.languageData || 'es',
        },
        headers,
      );

      if (response.success && !isEmpty(response.data)) {
        // sendErrorReport(true, 'deviceIset12');
        setDeviceId(response?.data?.id);
        if (isArray(connectedDeviceDetail)) {
          const obj = { ...response?.data };
          obj.product_id = item?.id;

          const arr = [...connectedDeviceDetail] || [];
          const index = findIndex(
            arr,
            lt => lt?.product_id === obj?.product_id,
          );
          if (index > -1) {
            arr[index] = obj;
          } else {
            arr.unshift(obj);
          }
          dispatch(BluetoothAction.setConnectedDeviceDetail(arr));
          dispatch(BluetoothAction.setConnectedDeviceDetails(arr));
          dispatch(BluetoothAction.setSwiperKey(obj?.product_id));
          dispatch(BluetoothAction.setActiveDeviceId(obj));
          // dispatch(BluetoothAction.setConnectedDeviceDetails(...connectedDeviceDetails, obj));
        }
        // dispatch(BluetoothAction.setConnectedDeviceDetail(deviceArr));
        // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
        // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
        if (isObject(item) && !isEmpty(item)) {
          dispatch(BluetoothAction.setDeviceID(''));
          // dispatch(BluetoothAction.setIsFromScanner(true));
          setTimeout(() => {
            // sendErrorReport(true, 'deviceIdSe4');
            dispatch(BluetoothAction.setDeviceID(item.id));
            dispatch(BluetoothAction.setLastDeviceId(item.id));
            dispatch(BluetoothAction.setIsConnectLoad(true));
            dispatch(BluetoothAction.setClickAddQr(false));
            navigation.navigate('Connect', {
              product_id: item?.id,
              device_id: response?.data?.id || deviceId,
              device_data: item?.name,
              device_ssid: item?.name,
              fromChildInfo,
            });
          }, 2500);
        } else {
          setIsScanning(false);
          // Toast.show("Can't find any device. Please try again");
          Toast.show(translate('cannotFindDevice'));
          startScan();
        }
      } else {
        Toast.show(response.message);
        setScanningText(translate('searchingQRCode'));
      }
    } catch (error) {
      console.log('error device detail ===', error);
      sendErrorReport(error, 'on_read_qr');
    }
  };

  const NoPermissionViewIos = (
    <View
      style={{
        width: Dimensions.get('window').width * 0.8,
        alignSelf: 'center',
      }}>
      <Text style={styles.qrTextStyle}>{translate('noCameraAcces')}</Text>
      <TouchableOpacity onPress={() => openSettings()}>
        <Text style={styles.openSettingsText}>{translate('openSettings')}</Text>
      </TouchableOpacity>
    </View>
  );
  const checkBTStatus = () => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          // setShowStep5d(true);
          sendErrorReport('step5c', 'step5c_device_list');
          setShowStep5c(true); // m
          break;
        case 'PoweredOn':
          sendErrorReport('step5d', 'step5d_device_list');
          setShowStep5d(true); // m
          break;
        default:
          break;
      }
    });
  };
  const renderItem = ({ item }) =>
    item?.name.includes('Baby_Auto') ? (
      <View
        style={{
          padding: 12,
          borderRadius: 8,
          backgroundColor: '#fff',
          shadowColor: '#000',
          margin: 8,
          sshadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
          {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
          <Text
            style={{
              fontSize: 18,
              marginStart: 8,
              flex: 1,
              fontWeight: '700',
              color: BaseColor.blackColor,
            }}>
            {item?.name == 'NO NAME' ? 'N/A' : item?.name}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor:
                item?.advertising?.isConnectable == 1 ? 'green' : 'green',
              padding: 8,
              borderRadius: 8,
            }}
            activeOpacity={0.7}
            onPress={() => {
              setSpin(true);
              onReadQRList(item);
            }}>
            {spin ? (
              <ActivityIndicator color={BaseColor.whiteColor} />
            ) : (
              <Text style={{ color: '#fff' }}>CONNECT</Text>
            )}
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              fontSize: 15,
              marginStart: 8,
              flex: 1,
              color: BaseColor.blackColor,
            }}>
            {item?.id}
          </Text>
        </View>
      </View>
    ) : null;

  const onRefresh = () => {
    setisRefreshing(true);
    BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped');
    });
    setTimeout(() => {
      startScan();
    }, 100);
  };

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={translate('connectToYourBabyAuto')}
        // backBtn
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.navigate(translate('home'));
          dispatch(BluetoothAction.setClickAddQr(false));
        }}
      />
      <View style={{ flex: 1 }}>
        <FlatList
          data={list}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{ flexGrow: 1 }}
          onRefresh={onRefresh}
          refreshing={isRefreshing}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{ fontWeight: 'bold', color: '#000' }}>
                No Device Available
              </Text>
            </View>
          )}
        />
      </View>
      <View style={{ margin: 40 }}>
        <Text>{translate('troubleText')}</Text>
        <TouchableOpacity
          style={{
            marginTop: 16,
            alignItems: 'center',
            backgroundColor: BaseColor.whiteColor,
          }}
          activeOpacity={0.7}
          onPress={() => {
            navigation.navigate('Troubleshoot');
          }}>
          <Text
            style={{
              color: BaseColor.whiteColor,
              textAlign: 'center',
              backgroundColor: BaseColor.blueDark,
              width: '40%',
              padding: 10,
              borderWidth: 0.5,
              borderRadius: 10,
              borderColor: BaseColor.blueDark,
            }}>
            {translate('troubleshoot')}
          </Text>
        </TouchableOpacity>
      </View>

      <StepPopup
        visible={showStep5}
        descriptionText={translate('step5')}
        image={require('../../assets/images/step5.png')}
        onNext={() => {
          setShowStep5(false);
          dispatch(setStep5Done(true));
          const index = findIndex(bleDeviceList, lt =>
            lt?.name.includes('Baby_Auto'),
          );
          console.log('show step 5 timeout', index);
          if (index > -1) {
            console.log('device found');
          } else {
            console.log('show step 5 timeout else', index);
            setTimeout(() => {
              // setShowStep5b(true);m
            }, 8000);
            // show troubleshoot popup
          }
        }}
        step5
      />
      <StepPopup
        visible={showStep5b}
        descriptionText={translate('step5b')}
        onNext={() => {
          setShowStep5b(false);
          dispatch(setStep5bDone(true));
        }}
        onTroubleshoot={() => {
          setShowStep5b(false);
          dispatch(setStep5bDone(true));
          checkBTStatus();
        }}
        trouble
      />
      <StepPopup
        visible={showStep5c}
        descriptionText={translate('step5c')}
        image={require('../../assets/images/step5c.jpg')}
        onNext={() => {
          setShowStep5c(false);
          dispatch(setStep5cDone(true));
        }}
        onTroubleshoot={() => {
          dispatch(setStep5cDone(true));
          setShowStep5c(false);
          if (Platform.OS == 'android') {
            sendErrorReport(true, 'requestToEnableD list');
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
              console.log('BLUETOOTH_CONNECT---', res);
              if (res === 'granted') {
                BluetoothStateManager.requestToEnable().then(result => {
                  console.log(
                    'BluetoothStateManager.requestToEnable -> result',
                    result,
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then(result => {
                console.log('BLUETOOTH_CONNECT----1', result);
              })
              .then(statuses => {
                console.log(
                  'BLUETOOTH_CONNECT--2',
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                );
              });
          } else {
            console.log('=====3333333');
            BluetoothStateManager.openSettings();
          }
        }}
        trouble
        settings
      />
      <StepPopup
        visible={showStep5d}
        descriptionText={translate('step5d')}
        image={require('../../assets/images/step2.png')}
        onNext={() => {
          dispatch(setStep5dDone(true));
          setShowStep5d(false);
          setShowStep5e(true);
        }}
      />
      <StepPopup
        visible={showStep5e}
        descriptionText={translate('step5e')}
        image={require('../../assets/images/step3.jpg')}
        onNext={() => {
          dispatch(setStep5eDone(true));
          setShowStep5e(false);
          setShowStep5f(true);
        }}
      />
      <StepPopup
        visible={showStep5f}
        descriptionText={translate('step5')}
        image={require('../../assets/images/step5.png')}
        onNext={() => {
          dispatch(setStep5fDone(true));
          setShowStep5f(false);
        }}
        step5
      />
    </View>
  );
};

export default BleList;
