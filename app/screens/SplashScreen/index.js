import React, { useEffect, useState } from 'react';
import {
  BackHandler,
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
  View,
  Platform,
  Linking,
  PermissionsAndroid,
  Alert,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { useDispatch, useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';
import BaseColor from '../../config/colors';
import BaseSetting from '../../config/setting';
import { getApiData } from '../../utils/apiHelper';
import AuthAction from '../../redux/reducers/auth/actions';
import { sendErrorReport } from '../../utils/commonFunction';
import CAlert from '../../components/CAlert';
import { translate } from '../../lang/Translate';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

/**
 *
 *@module SplashScreen
 *
 */
const SplashScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const locationDisclouser = useSelector(
    state => state.auth.locationDisclouser,
  );
  const animation = useSharedValue({ width: 5000, height: 5000 });
  // const animation = {width: 5000, height: 5000};
  const walkthrough = useSelector(state => state.auth.walkthrough);
  const { setLocationDisclouser } = AuthAction;
  const accessToken = useSelector(state => state.auth.accessToken);
  const [dsettings, setDSettings] = useState(false);
  const animationStyle = useAnimatedStyle(() => ({
    width: withTiming(animation.value.width, {
      duration: 2000,
    }),

    height: withTiming(animation.value.height, {
      duration: 2000,
    }),
  }));

  useEffect(() => {
    animation.value = { width: 0, height: 0 };

    setTimeout(() => {
      if (walkthrough) {
        navigation.navigate('Walkthrough');
        return;
      }
      if (accessToken) {
        navigation.navigate('DrawerNav');
      } else {
        navigation.navigate('RedirectLS');
      }
    }, 3000);
  }, []);

  function handleBackButtonClick() {
    return true;
  }

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackButtonClick,
    );
    return () => backHandler?.remove();
  }, []);

  /** this function for get LanguageList
   * @function getLanguageList
   * @param {object} data {}
   */
  const getLanguageList = () => {
    getApiData(BaseSetting.endpoints.getLanguageList, 'post', {})
      .then(response => {
        console.log('.then -> response', response);
        if (response?.success) {
          dispatch(AuthAction.setLanguageList(response.data));
        }
      })
      .catch(err => {
        console.log('.catch -> err', err);
        Toast.show('Something went wrong while getting lanugage list');
        sendErrorReport(err, 'getting_language');
      });
  };
  const showAlert = () => {
    Alert.alert(
      'Linko BabyAuto',
      'Linko BabyAuto collects location data to enable sending emergency SMS, handling from smart safety cushion even when the app is closed or not in use.',
      [
        {
          text: 'Close',
          style: 'cancel',
          onPress: () => dispatch(setLocationDisclouser(false)),
        },
      ],
      {
        cancelable: true,
      },
    );
  };
  useEffect(() => {
    if (Platform.OS === 'android') {
      if (locationDisclouser) {
        showAlert();
      }
    }
    getLanguageList();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: BaseColor.whiteColor,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <StatusBar
        backgroundColor="transparent"
        barStyle="dark-content"
        translucent
      />
      <Image
        source={require('../../assets/images/logo.png')}
        style={{ height: 150, width: 150 }}
      />
      <Animated.View
        style={[
          {
            backgroundColor: BaseColor.orange,
            position: 'absolute',
            borderRadius: 5000,
          },
          animationStyle,
        ]}
      />

      {/* {Platform.OS === "android" && (
        <CAlert
          visible={locationDisclouser}
          type="settingAlert"
          onRequestClose={() => dispatch(setLocationDisclouser(false))}
          agreeTxt={translate("locationSettingsAlert")}
          onOkPress={() => {
            Linking.openSettings();
            dispatch(setLocationDisclouser(false));
          }}
          alertTitle={translate("locationSettingsTitle")}
          onCancelPress={() => {
            dispatch(setLocationDisclouser(false));
          }}
        />
      )} */}
    </View>
  );
};

export default SplashScreen;
