/* eslint-disable quotes */
import { useTheme } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Modal,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useSelector } from "react-redux";
import { flattenDeep, isArray, isEmpty } from "lodash";
import CButton from "../../components/CButton";
import CHeader from "../../components/CHeader";
import DropDown from "../../components/DropDown";
import GradientBack from "../../components/gradientBack";
import BaseSetting from "../../config/setting";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import styles from "./styles";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";

/**
 *
 *@module ProductCatalouge
 *
 */
export default function ProductCatalouge({ navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const token = useSelector((state) => state.auth.accessToken);
  const brandToken = useSelector((state) => state.auth.brandToken);

  const [filterModal, setfilterModal] = useState(false);
  const [selectedCat, setselectedCat] = useState("");
  const [selectedFilter, setselectedFilter] = useState("");
  const [selectedChara, setselectedChara] = useState("");
  const [filterData, setFilterData] = useState("");
  const [sortingObj, setSortingObj] = useState({});

  const [productList, setproductList] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const [pageLoad, setPageLoad] = useState(true);
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);
  const [ecomCatalougeList, setEcomCatalougeList] = useState([]);
  const [inventoryQuantity, setInventoryQuantity] = useState(0);
  const [relInfo, setRel] = useState("");
  const [pageInfo, setPageInfo] = useState("");
  const [filterListF, setFilterList] = useState([]);

  const characterstics = [
    // { id: 1, name: "Best Seller" },
    { id: 1, name: "Alphabetically - A Z" },
    { id: 2, name: "Alphabeticall - Z A" },
    // { id: 4, name: "Price, low to high" },
    // { id: 5, name: "Price, high to low" },
    { id: 3, name: "Date - Old to recent" },
    { id: 4, name: "Date - Recent to old" },
  ];
  useEffect(() => {
    setPage(1);
    // setproductList([]);
    setEcomCatalougeList([]);
    // getProductList();
    getEComCatalougeList();
  }, []);

  /** this function for get Product List
   * @function getEComProductList
   * @param {object} data token, per_page, page, category_id
   */
  const getEComCatalougeList = (type) => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    setPageLoad(true);
    const data = {};
    getApiData(BaseSetting.endpoints.ecomCatalougeList, "GET", data, headers)
      .then((response) => {
        if (response.success) {
          console.log("--------response catalouge----", response);
          const tempPArr = flattenDeep([
            type === "filter" || type === "reset" ? [] : ecomCatalougeList,
            response.data,
          ]);

          setTimeout(() => {
            setEcomCatalougeList(tempPArr);
          }, 0);

          console.log("------ecomCatalougeList list", tempPArr);
          setPageLoad(false);
        } else {
          Toast.show(response.message);
        }
      })
      .catch((err) => {
        setPageLoad(false);
        console.log("ERRR---------", err);
        Toast.show("Something went wrong while getting ECOM Catalouge list");
        sendErrorReport(err, "get_product_list");
      });
  };

  // this function is used when list data reached to limit while scrolling
  const onEndReached = () => {
    setNextLoading(true);

    // getEComCatalougeList();

    // getEComProductList();
    console.log(".then -> on end called -------");
    if (nextPage) {
      const tempPage = page + 1;
      console.log(".then -> on end -------", nextPage);
      setPage(tempPage);
      // getEComProductList();
    }
  };

  // this function is used when list is refresh from top
  const onRefresh = React.useCallback(() => {
    // setproductList([]);
    // getProductList();
    setRel("");
    setPageInfo("");
    getEComCatalougeList();
  }, []);

  // this function is used to render products listing design
  const renderProducts = ({ item, index }) => (
    <TouchableOpacity
      style={[
        {
          backgroundColor: BaseColor.whiteColor,
          paddingBottom: 20,
          width: Dimensions.get("window").width / 2,
        },
      ]}
      onPress={() => {
        navigation.navigate("Products", { catalouge: item });
      }}
    >
      <View style={{ marginStart: 15 }}>
        <View style={{ width: Dimensions.get("window").width / 2.4 }}>
          <Image
            source={
              item?.image
                ? { uri: item?.image.src }
                : require("../../assets/images/logo.png")
            }
            style={[
              {
                borderColor: BaseColor.black60,
                height: Dimensions.get("window").height / 5,
                width: Dimensions.get("window").width / 2.4,
                resizeMode: "cover",
              },
            ]}
          />
        </View>
        <View style={{ justifyContent: "space-around" }}>
          <View style={{ paddingTop: 5 }}>
            <Text style={[styles.nameStyle, { color: BaseColor.blackColor }]}>
              {item?.title}
            </Text>
          </View>
        </View>

        {/* <View
              style={{
                ...styles.rowStyle,
                alignItems: "center",
                alignContent: "center",
              }}
            >
              {getStars(5)}
              <Text style={{ paddingLeft: 5, fontSize: 10 }}>6 reviews</Text>
            </View> */}
      </View>
    </TouchableOpacity>
  );

  const renderProducts2 = ({ item, index }) => (
    <TouchableOpacity
      style={[
        {
          // backgroundColor: BaseColor.whiteColor,
          paddingBottom: 20,
          width: Dimensions.get("window").width / 2,
        },
      ]}
      onPress={() => {
        navigation.navigate("Products", { catalouge: item });
      }}
    >
      <View style={{ marginStart: 15 }}>
        <View style={{ width: Dimensions.get("window").width / 2.4 }}>
          <ImageBackground
            style={{
              flex: 1,
              resizeMode: "cover",
              width: Dimensions.get("window").width / 2.3,
              height: Dimensions.get("window").width / 3,
              backgroundColor: "rgba(0,0,0,.6)",
            }}
            // imageStyle={{ opacity: 0.6 }}
            source={
              item?.image
                ? { uri: item?.image.src }
                : require("../../assets/images/logo.png")
            }
          >
            <View
              style={{
                backgroundColor: "rgba(0,0,0,.5)",
                width: "100%",
                height: "100%",
              }}
            >
              <Text
                style={[
                  styles.nameStyle,
                  {
                    color: BaseColor.whiteColor,
                    position: "absolute",
                    left: 5,
                    bottom: 10,
                  },
                ]}
              >
                {item?.title}
              </Text>
            </View>
          </ImageBackground>
        </View>
      </View>
    </TouchableOpacity>
  );
  // );

  enableAnimateInEaseOut();

  return (
    <View style={[styles.root, { backgroundColor: BaseColor.whiteColor }]}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <CHeader
          title={translate("Catalogue")}
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
          // rightIconName="filter"
          // onRightPress={() => {
          //   setfilterModal(true);
          // }}
        />
        {isEmpty(ecomCatalougeList) ? <Text>{ecomCatalougeList}</Text> : null}
        <FlatList
          keyExtractor={(item, index) => index}
          // data={productList}
          numColumns={2}
          data={ecomCatalougeList}
          renderItem={renderProducts2}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReachedThreshold={0.4}
          onEndReached={onEndReached}
          style={{ marginTop: 15 }}
          // ListFooterComponent={renderListFooter}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              {pageLoad ? (
                <ActivityIndicator color={BaseColor.blackColor} />
              ) : (
                <Text
                  style={{
                    // padding: 8,
                    // width: "100%",
                    fontSize: 16,
                    color: BaseColor.blackColor,
                    textAlign: "center",
                  }}
                >
                  {translate("noProducts")}
                </Text>
              )}
            </View>
          )}
        />
      </View>
      <Modal
        style={{ flex: 1 }}
        visible={filterModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setfilterModal(false);
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: "flex-end",
          }}
          onPress={() => {
            setfilterModal(false);
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.blueDark,
              padding: 24,
              borderTopEndRadius: 16,
              borderTopStartRadius: 16,
            }}
          >
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 16,
                marginVertical: 8,
                fontFamily: FontFamily.default,
                fontWeight: "bold",
              }}
            >
              FILTER
            </Text>
            {/* <DropDown
              placeholder="Select Category"
              data={categoryList}
              style={{ borderRadius: 12, marginEnd: 4 }}
              valueProp="category_name"
              onSelect={(val) => {
                // setHeight(val);
                setselectedCat(val);
              }}
              selectedObject={selectedCat}
            /> */}
            <DropDown
              placeholder="Sorting"
              data={characterstics}
              style={{ borderRadius: 12, marginEnd: 4 }}
              valueProp="name"
              onSelect={(val) => {
                setselectedChara(val);
                console.log("---------on select--", val, val.name);
                let obj = {};
                if (val.name === "Alphabetically - A Z") {
                  obj = { sort_value: "title", sort_type: "ascend" };
                } else if (val.name === "Alphabeticall - Z A") {
                  obj = { sort_value: "title", sort_type: "desc" };
                } else if (val.name === "Date - Old to recent") {
                  obj = {
                    sort_value: "created_at",
                    sort_type: "ascend",
                  };
                } else if (val.name === "Date - Recent to old") {
                  obj = { sort_value: "created_at", sort_type: "desc" };
                }
                setSortingObj(obj);
                console.log("---------filter---val--", obj, sortingObj);
              }}
              selectedObject={selectedChara}
            />
            <View style={{ marginTop: 16 }}>
              <DropDown
                placeholder="Product Type"
                data={filterListF}
                style={{ borderRadius: 12, marginEnd: 4 }}
                valueProp="product_type"
                onSelect={(val) => {
                  setPageInfo("");
                  setselectedFilter(val);
                  setFilterData(val.product_type);
                }}
                selectedObject={selectedFilter}
              />
            </View>
            <View style={{ flexDirection: "row", marginBottom: 16 }}>
              <CButton
                title="SEARCH"
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 8,
                  marginTop: 16,
                  marginEnd: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.blackColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  console.log("-------in search-");
                  // navigation.navigate('Login');
                  setfilterModal(false);
                  // setproductList([]);
                  setEcomCatalougeList([]);
                  setPage(1);
                  // setPageInfo("");
                  setPageLoad(true);
                  // getProductList("filter");
                  getEComCatalougeList("filter");
                }}
              />
              <CButton
                title="RESET"
                style={{
                  backgroundColor: BaseColor.orange,
                  borderRadius: 8,
                  marginTop: 16,
                  marginStart: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  setselectedCat({});
                  setPage(1);
                  setRel("");
                  setPageInfo("");
                  getEComCatalougeList("reset");
                  // getProductList("reset");
                  // setfilterModal(false);
                  // navigation.navigate('Login');
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
