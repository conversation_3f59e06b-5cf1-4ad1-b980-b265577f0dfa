/* eslint-disable no-nested-ternary */
import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const CHeader = props => {
  const {
    image,
    title,
    customLeftIcon,
    customRightIcon,
    leftIconName,
    rightIconName,
    onLeftPress,
    onRightPress,
    backBtn,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;
  const insets = useSafeAreaInsets();

  const {notificationCount} = useSelector(state => state.auth);
  return (
    <View
      style={{
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        // height: 80,
        alignContent: 'center',
        paddingTop: insets?.top > 0 ? insets?.top + 16 : 16,
        paddingHorizontal: 16,
        paddingBottom: 16,
      }}>
      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {leftIconName || backBtn ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onLeftPress}
            style={{
              backgroundColor: BaseColor.whiteColor,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: leftIconName === 'left-arrow' ? 0 : 0.5,
              borderColor:
                leftIconName === 'left-arrow' ? null : BaseColor.textGrey,
            }}>
            {backBtn ? (
              <FAIcon
                name="angle-left"
                size={24}
                color={BaseColor.blackColor}
              />
            ) : leftIconName ? (
              <>
                {leftIconName === 'settings-2' &&
                notificationCount.chat_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'red',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.chat_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={leftIconName}
                  size={leftIconName === 'left-arrow' ? 22 : 18}
                  color={BaseColor.blackColor}
                />
              </>
            ) : null}
            {/* {leftIconName } */}
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={{flex: 1, alignItems: 'center'}}>
        {image ? (
          <Image style={{height: 60, width: 60}} source={image} />
        ) : (
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: BaseColor.blackColor,
              fontSize: 18,
              fontWeight: 'bold',
              letterSpacing: 2,
              paddingHorizontal: 24,
            }}
            numberOfLines={1}>
            {title}
          </Text>
        )}
      </View>

      <View
        style={{
          height: 40,
          width: 40,
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {rightIconName ? (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onRightPress}
            style={{
              backgroundColor: BaseColor.whiteColor,
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 0.5,
              borderColor: BaseColor.textGrey,
            }}>
            {rightIconName ? (
              <>
                {rightIconName === 'notifications-bell-button' &&
                notificationCount.notification_count > 0 ? (
                  <View
                    style={{
                      position: 'absolute',
                      backgroundColor: 'red',
                      width: 18,
                      height: 18,
                      top: -3,
                      right: -3,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        fontSize: 12,
                      }}
                      numberOfLines={1}>
                      {notificationCount.notification_count}
                    </Text>
                  </View>
                ) : null}
                <CustomIcon
                  name={rightIconName}
                  size={rightIconName === 'clear' ? 24 : 18}
                  color={
                    rightIconName === 'check'
                      ? BaseColor.blueDark
                      : BaseColor.blackColor
                  }
                />
              </>
            ) : null}
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default CHeader;
