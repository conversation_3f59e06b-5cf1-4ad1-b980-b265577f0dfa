import { useEffect, useState } from 'react';
import messaging from '@react-native-firebase/messaging';
import { showNotification } from '../services/NotificationHelper';
import PushNotification from 'react-native-push-notification';
import { useDispatch, useSelector } from 'react-redux';
import { isObject, isEmpty, isString } from 'lodash';
import { store } from '../../redux/store/configureStore';
import NotificationAction from '../../redux/reducers/notification/actions';
import AuthActions from '../../redux/reducers/auth/actions';
import { Platform } from 'react-native';
import BaseSetting from '../../config/setting';
import { getApiData } from '../../utils/apiHelper';
import { addAction, sendErrorReport } from '../../utils/commonFunction';
import FirmwareModal from '../FirmwareModal';
import CarPlayService from '../../services/CarPlayService';
import InAppModal from '../InAppModal';
// import { withInAppNotification } from '../../lib/react-native-in-app-notification';
// import {
//   callCommonHandleNotificationfunction,
//   handleNotification,
// } from '@utils/CommonFunction';

const { setUUid, setNotiCount } = AuthActions;
const IOS = Platform.OS === 'ios';

/**
 *firebase notification
 * @function  RemotePushController
 */
const RemotePushController = () => {
  const dispatch = useDispatch();
  const { accessToken } = useSelector(state => state.auth);
  const { fcmToken } = useSelector(state => state.notification);
  const [firmwareModal, setFirmwareModal] = useState(false);
  const [notificationDetail, setNotificationDetail] = useState({});
  const [postModal, setPostModal] = useState(false);

  // this function for handle notification
  async function handleIncomingNotification(notificationData, bool) {
    const nData =
      isString(notificationData?.objData) && !isEmpty(notificationData?.objData)
        ? JSON.parse(notificationData?.objData) || notificationData?.objData
        : {};

    // console.log("notification data------", notificationData, nData);

    // Send notification to CarPlay
    try {
      if (Platform.OS === 'ios' && CarPlayService.isCarPlayConnected()) {
        const carPlayNotification = {
          title: notificationData.title || 'ChillBaby',
          message:
            notificationData.body ||
            notificationData.message ||
            'New notification',
          type: notificationData.type === 'firmware' ? 'warning' : 'info',
          data: {
            ...notificationData,
            category: 'app_notification',
          },
        };
        CarPlayService.showNotification(carPlayNotification);
      }
    } catch (error) {
      console.error('Error sending notification to CarPlay:', error);
    }

    if (notificationData.type === 'firmware') {
      if (!firmwareModal) {
        setNotificationDetail(notificationData);
        setFirmwareModal(true);
        addAction(nData, 'viewed', accessToken);
      }
    } else {
      setNotificationDetail(notificationData);
    }
    if (
      isObject(nData) &&
      !isEmpty(nData) &&
      (nData.campaign_type === 'push_message' ||
        notificationData.type === 'test_post')
    ) {
      if (!postModal) {
        setNotificationDetail(nData);
        setPostModal(true);
      }

      if (nData.campaign_type === 'push_message') {
        addAction(nData, 'viewed', accessToken);
      }
    } else {
      setNotificationDetail(notificationData);
    }
  }

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   * <AUTHOR>
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
      }
    } catch (error) {
      sendErrorReport(error, 'get_device_list');
    }
  }

  /** this function for storing FCM Token
   * @function getBadgeCount
   * @param token token id of the device got from firebase
   * <AUTHOR>
   */
  const storeFCMToken = async token => {
    try {
      await store.dispatch(NotificationAction.setFcmToken(token));
      if (accessToken) {
        dispatch(setUUid(fcmToken));
        const data = {
          token: fcmToken,
          platform: IOS ? 'IOS' : 'ANDROID',
        };
        try {
          const response = await getApiData(
            BaseSetting.endpoints.addToken,
            'POST',
            data,
            {
              'Content-Type': 'application/json',
              authorization: accessToken ? `Bearer ${accessToken}` : '',
            },
          );

          console.log(
            '🚀🚀🚀🚀FCM Token Saved---StoreFCMToken---response---',
            response,
          );
        } catch (err) {
          sendErrorReport(err, 'send_fcm_token');
        }
      }
    } catch (error) {}
  };

  useEffect(() => {
    PushNotification.configure({
      // (required) Called when a remote or local notification is opened or received
      onNotification: function (notification) {
        store.dispatch(NotificationAction.onNotificationOpen(notification));

        if (notification?.userInteraction && notification?.foreground) {
          // This is for handling and making some action when clicked by user
          // callCommonHandleNotificationfunction(notification);
        }
      },

      popInitialNotification: true,
      requestPermissions: true,
    });

    PushNotification.createChannel(
      {
        channelId: 'default-channel-id',
        channelName: 'Default channel',
        channelDescription: 'A default channel',
        soundName: 'default',
        importance: 4,
        vibrate: true,
      },
      created =>
        console.log(`createChannel 'default-channel-id' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );

    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage,
      );
      // This is for handling and making some action when clicked by user
      if (isObject(remoteMessage?.data) && !isEmpty(remoteMessage?.data)) {
        handleIncomingNotification(remoteMessage?.data, false);
      }
    });
    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log(
            'Notification caused app to open from quit state:',
            remoteMessage,
          );
          // This is for handling and making some action when clicked by user
          // callCommonHandleNotificationfunction(remoteMessage);
        }
      });

    // Get the device token
    messaging()
      .getToken()
      .then(token => {
        console.log('🚀🚀🚀🚀🚀 FCM Token', token);
        return storeFCMToken(token);
      });

    // Listen to whether the token changes
    return messaging().onTokenRefresh(token => {
      storeFCMToken(token);
    });
  }, [accessToken]);

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      handleIncomingNotification(remoteMessage?.data);
      store.dispatch(NotificationAction.updateNotification(remoteMessage));
      store.dispatch(
        NotificationAction.setBadgeCount(remoteMessage?.data?.badge),
      );
      showNotification(remoteMessage, true);
    });
    return unsubscribe;
  }, []);

  if (postModal) {
    return (
      <InAppModal
        visible={postModal}
        detail={notificationDetail}
        campaignType={
          notificationDetail?.campaign_type === 'feed_post' ? 'feed_post' : ''
        }
        button={notificationDetail?.button_info || []}
        title={
          !isEmpty(notificationDetail) ? notificationDetail?.text_info : {}
        }
        image={
          !isEmpty(notificationDetail)
            ? notificationDetail?.campaign_type === 'feed_post'
              ? notificationDetail.media_link
              : notificationDetail?.post_file
            : ''
        }
        position={
          !isEmpty(notificationDetail)
            ? notificationDetail?.message_position
            : ''
        }
        onClose={() => {
          setPostModal(false);
          getBadgeCount();
          addAction(notificationDetail, 'clicked', accessToken);
        }}
      />
    );
  }

  if (firmwareModal) {
    return (
      <FirmwareModal
        visible={firmwareModal}
        detail={notificationDetail}
        title={!isEmpty(notificationDetail) ? notificationDetail?.title : {}}
        onClose={() => {
          setFirmwareModal(false);
          getBadgeCount();
          addAction(notificationDetail, 'clicked', accessToken);
        }}
      />
    );
  }
  return null;
};

export default RemotePushController;
