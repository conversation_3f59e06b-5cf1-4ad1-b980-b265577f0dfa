import PushNotification from 'react-native-push-notification';
import { Platform } from 'react-native';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
/**
 *
 *@function showNotification
 *
 */
export const showNotification = (remoteMessage: any, isForeground = false) => {
  const { data, notification } = remoteMessage;

  // For Handling Comet Chat Notifications
  if (!remoteMessage.hasOwnProperty('notification')) {
    console.log('No Notification key present');
    const { title, alert, message } = data;
    let notificationTitle = title;
    console.log('title>>>', title);
    console.log('alert>>>', alert);
    console.log('message>>>', message ? JSON.stringify(message) : '');
    if (message) {
      sendLocalNotification(notificationTitle, alert, message);
    }
  } else {
    console.log('Notification key present');
    const { body, title } = notification;
    sendLocalNotification(title, body, data);
  }
};

const sendLocalNotification = (title: any, body: any, data: any) => {
  if (Platform.OS === 'android') {
    PushNotification.localNotification({
      channelId: 'default-channel-id',
      largeIcon: 'ic_launcher',
      smallIcon: 'ic_notification',
      title: title,
      message: body,
      userInfo: data,
    });
  } else {
    let details = {
      alertTitle: title,
      alertBody: body,
      userInfo: data,
    };

    console.log('PushNotificationIOS ===', PushNotificationIOS);
    PushNotificationIOS.presentLocalNotification(details);
  }
};
