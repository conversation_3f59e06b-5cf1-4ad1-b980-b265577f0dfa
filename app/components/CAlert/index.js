/* eslint-disable quotes */
import { useTheme } from '@react-navigation/native';
import React, { useEffect, useState, useRef } from 'react';
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  View,
  AppState,
  ProgressBarAndroid,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';
// import { AnimatedCircularProgress } from "react-native-circular-progress";
import AsyncStorage from '@react-native-async-storage/async-storage';
import _BackgroundTimer from 'react-native-background-timer';
import { CustomIcon } from '../../config/LoadIcons';
import { FontFamily } from '../../config/typography';
import { translate } from '../../lang/Translate';
import CButton from '../CButton';
import CInput from '../CInput';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import { sendErrorReport } from '../../utils/commonFunction';

let interval = null;

const CAlert = props => {
  const {
    setLowAlert,
    setHighAlert,
    setLowHAlert,
    setHighHAlert,
    setIsConvert,
  } = bluetoothActions;
  const colors = useTheme();
  const dispatch = useDispatch();
  const BaseColor = colors.colors;
  const [stateData, setStateData] = useState({
    lowTemp: '',
    highTemp: '',
    lowHumdt: '',
    highHumdt: '',
  });
  const {
    lowAlert,
    highAlert,
    lowHAlert,
    highHAlert,
    isConvert,
    timerVal,
    isBleConnected,
    smsSentAndroidBg,
  } = useSelector(state => state.bluetooth);
  const { isFarenheit } = useSelector(state => state.auth);

  const {
    visible,
    onRequestClose,
    alertMessage,
    alertName,
    alertTitle,
    onCancelPress = () => {},
    onOkPress = () => {},
    agreeTxt = translate('alertOkBtn'),
    cancelTxt = translate('alertCancelBtn'),
    type = '',
    loader = false,
    timerValue,
  } = props;
  const [time, setTime] = useState(timerVal);
  // const [timerA, setTimerA] = useState(AsyncStorage.getItem("timer") || 60);
  const [appState, setAppState] = useState(true);
  // useEffect(() => {
  //   if (type === "leftChild") {
  //     console.log("ddddddd timer start", type);
  //     const timerId = setInterval(() => {
  //       timerRef.current -= 1;
  //       if (timerRef.current < 0) {
  //         clearInterval(timerId);
  //       } else {
  //         setTime(timerRef.current);
  //       }
  //     }, 900);
  //     return () => {
  //       clearInterval(timerId);
  //     };
  //   }
  // }, [type]);

  // useEffect(() => {
  //   if (type === "leftChild") {
  //     if (timer > 0) {
  //       interval = setInterval(() => setTimer((p) => p - 1), 1000);
  //     }
  //   }
  // }, [type, timer]);
  const asyncTimer = async () => {
    await AsyncStorage.getItem('timer');
  };

  useEffect(() => {
    if (type === 'leftChild') {
      sendErrorReport(timerValue, 'timervalue');
      sendErrorReport(timerVal, 'timerRedux');
      if (smsSentAndroidBg === false) {
        sendErrorReport(timerVal, 'smsSentAndroidBg');
      }
      setTime(timerVal);
      interval = _BackgroundTimer.setInterval(() => setTime(p => p - 1), 1000);
    }
  }, [type]);

  useEffect(() => {
    if (time <= 0) {
      _BackgroundTimer.clearInterval(interval);
      _BackgroundTimer.stop();
      clearInterval(interval);
      setTime(0);
      if (type === 'leftChild') {
        // sendErrorReport(time, "timerValueInAlert");
      }
    }
  }, [time]);

  const getConvertedTemp = realTemp => {
    if (isFarenheit && isConvert) {
      return ((Number(realTemp) * 9) / 5 + 32).toFixed(0);
    }
    if (!isFarenheit && isConvert) {
      return (((Number(realTemp) - 32) * 5) / 9).toFixed(0);
    }
    return realTemp;
  };

  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
        setAppState(true);
      } else if (nextAppState === 'background') {
        console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
        setAppState(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);
  useEffect(() => {
    if (visible) {
      setStateData({
        // lowTemp: lowAlert,
        // highTemp: highAlert,
        lowTemp: getConvertedTemp(lowAlert),
        highTemp: getConvertedTemp(highAlert),
        lowHumdt: lowHAlert,
        highHumdt: highHAlert,
      });
    }
  }, [visible]);

  // this function for handle Ok button press
  function handleOk() {
    if (type === 'temp') {
      dispatch(setLowAlert(stateData.lowTemp));
      dispatch(setHighAlert(stateData.highTemp));
    } else if (type === 'humdt') {
      dispatch(setLowHAlert(stateData.lowHumdt));
      dispatch(setHighHAlert(stateData.highHumdt));
    }
    setTimeout(() => {
      onOkPress();
    }, 200);
  }

  if (!visible) {
    return <></>;
  }

  return (
    <Modal
      visible={visible}
      onRequestClose={onRequestClose}
      style={{ flex: 1 }}
      transparent
      animationType="slide">
      <KeyboardAvoidingView
        style={{
          flex: 1,
          overflow: 'hidden',
        }}
        behavior={Platform.OS === 'ios' ? 'height' : null}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              backgroundColor: BaseColor.black40,
              alignItems: 'center',
            }}>
            <View
              style={{
                borderRadius: 16,
                backgroundColor: BaseColor.whiteColor,
                padding: 22,
                alignItems: 'center',
                paddingTop: 48,
                width: '90%',
              }}>
              <View
                style={{
                  backgroundColor: BaseColor.alertRed,
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 64,
                  width: 64,
                  borderRadius: 64,
                  position: 'absolute',
                  top: -25,
                }}>
                <CustomIcon
                  name="warning"
                  size={24}
                  color={BaseColor.whiteColor}
                />
              </View>
              <Text
                style={{
                  color: BaseColor.black90,
                  fontSize: 16,
                  fontFamily: FontFamily.default,
                  fontWeight: 'bold',
                }}>
                {alertName}
              </Text>
              <Text
                style={{
                  color: BaseColor.black90,
                  fontSize: 16,
                  fontFamily: FontFamily.default,
                  fontWeight: 'bold',
                  textAlign: 'center',
                }}>
                {alertTitle}
              </Text>
              {type === 'leftChild' ? (
                // <Text
                //   style={{
                //     textAlign: "right",
                //     width: "76%",
                //     marginTop: 16,
                //     fontFamily: FontFamily.default,
                //     color: BaseColor.blackColor,
                //     position: "absolute",
                //     right: 20,
                //     top: 10,
                //     // fontWeight: "600",
                //   }}
                // >
                //   {timer}
                // </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                  }}>
                  <Text>Time </Text>

                  {Platform.OS === 'android' ? (
                    <>
                      {/* <AnimatedCircularProgress
                        size={40}
                        width={5}
                        fill={timerValue}
                        duration={timerValue}
                        tintColor="#00e0ff"
                        backgroundColor="#3d5875"
                        rotation={60}
                      /> */}
                      {/* {smsSentAndroidBg === false ? (
                        <CountdownCircleTimer
                          isPlaying
                          duration={60}
                          colors={["#004777", "#F7B801", "#A30000", "#A30000"]}
                          colorsTime={[7, 5, 2, 0]}
                          size={40}
                          strokeWidth={5}
                        >
                          {({ remainingTime }) => <Text>{remainingTime}</Text>}
                        </CountdownCircleTimer>
                      ) : (
                        <>
                          <ProgressBarAndroid />
                          <Text
                            style={{
                              position: "absolute",
                              top: 14,
                              left: time < 10 ? 55 : 50,
                            }}
                          >
                            {time.toString()}
                          </Text>
                        </>
                      )} */}
                      <>
                        <ProgressBarAndroid />
                        <Text
                          style={{
                            position: 'absolute',
                            top: 14,
                            left: timerValue < 10 ? 55 : 50,
                          }}>
                          {timerValue.toString()}
                        </Text>
                      </>
                    </>
                  ) : (
                    <CountdownCircleTimer
                      isPlaying
                      duration={60}
                      colors={['#004777', '#F7B801', '#A30000', '#A30000']}
                      colorsTime={[7, 5, 2, 0]}
                      size={40}
                      strokeWidth={5}>
                      {({ remainingTime }) => <Text>{remainingTime}</Text>}
                    </CountdownCircleTimer>
                  )}
                  <Text> to automatically send emergency SMS</Text>
                </View>
              ) : null}
              {type === 'settingAlert' ? (
                <Text
                  style={{
                    textAlign: 'center',
                    width: '76%',
                    marginTop: 16,
                    fontFamily: FontFamily.default,
                    color: BaseColor.blackColor,
                  }}>
                  {translate('emergencySettings')}
                  <Text
                    style={{
                      textAlign: 'center',
                      width: '76%',
                      marginTop: 16,
                      fontFamily: FontFamily.default,
                      color: BaseColor.blackColor,
                      // fontWeight: "600",
                    }}>
                    {translate('emergencyFeatures')}
                  </Text>
                </Text>
              ) : (
                <Text
                  style={{
                    textAlign: 'center',
                    width: '76%',
                    marginTop: 16,
                    fontFamily: FontFamily.default,
                    color: BaseColor.blackColor,
                  }}>
                  {alertMessage}
                </Text>
              )}
              {type === 'temp' ? (
                <View style={{ width: '100%' }}>
                  <CInput
                    keyboardType="numeric"
                    placeholder="Low Temperature"
                    value={stateData.lowTemp}
                    onChangeText={val => {
                      setStateData({
                        ...stateData,
                        lowTemp: val,
                      });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: '100%',
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                  <CInput
                    keyboardType="numeric"
                    placeholder="High Temperature"
                    value={stateData.highTemp}
                    onChangeText={val => {
                      setStateData({
                        ...stateData,
                        highTemp: val,
                      });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: '100%',
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                      marginTop: 10,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                </View>
              ) : null}
              {type === 'humdt' ? (
                <View style={{ width: '100%' }}>
                  <CInput
                    keyboardType="numeric"
                    placeholder="Low Humidity"
                    value={stateData.lowHumdt}
                    onChangeText={val => {
                      setStateData({ ...stateData, lowHumdt: val });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: '100%',
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                  <CInput
                    keyboardType="numeric"
                    placeholder="High Humidity"
                    value={stateData.highHumdt}
                    onChangeText={val => {
                      setStateData({ ...stateData, highHumdt: val });
                    }}
                    inputStyle={{ color: BaseColor.blackColor }}
                    textInputWrapper={{
                      width: '100%',
                      borderWidth: 1,
                      borderColor: BaseColor.blackColor,
                      marginTop: 10,
                    }}
                    hideLeftIcon
                    placeholderTextColor={BaseColor.textGrey}
                  />
                </View>
              ) : null}
              <View style={{ flexDirection: 'row', marginTop: 24 }}>
                {type === 'tempAlert' ? null : (
                  <CButton
                    title={cancelTxt}
                    style={{
                      flex: 1,
                      marginEnd: 8,
                      borderWidth: 1,
                      borderColor: BaseColor.blueDark,
                      backgroundColor: BaseColor.whiteColor,
                      elevation: 0,
                      shadowOffset: {
                        width: 0,
                        height: 0,
                      },
                      shadowOpacity: 0.0,
                    }}
                    titleStyle={[
                      { color: BaseColor.blueDark },
                      type === 'leftChild'
                        ? { fontSize: 11, textAlign: 'center' }
                        : {},
                    ]}
                    onPress={() => {
                      onCancelPress();
                    }}
                  />
                )}
                <CButton
                  title={agreeTxt}
                  iconBg={BaseColor.whiteColor}
                  style={{
                    flex: 1,
                    marginStart: 8,
                    borderWidth: 1,
                    borderColor: BaseColor.blueDark,
                    backgroundColor: BaseColor.blueDark,
                    elevation: 0,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    shadowOpacity: 0.0,
                  }}
                  loader={loader}
                  titleStyle={[
                    { color: BaseColor.whiteColor },
                    type === 'leftChild'
                      ? { fontSize: 11, textAlign: 'center' }
                      : {},
                  ]}
                  onPress={handleOk}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default CAlert;
