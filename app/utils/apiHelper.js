import { isEmpty, isObject, isString } from 'lodash';
// import Bugsnag from '@bugsnag/react-native';
import { useDispatch } from 'react-redux';
import BaseSetting from '../config/setting';
import { store } from '../redux/store/configureStore';
import AuthAction from '../redux/reducers/auth/actions';
import actions from '../redux/reducers/auth/actions';
import { sendErrorReport } from './commonFunction';

export async function removeToken(token, uuid) {
  try {
    const response = await getApiData(
      BaseSetting.endpoints.deleteToken,
      'POST',
      {
        token: uuid,
      },
      {
        'Content-Type': 'application/json',
        authorization: token ? `Bearer ${token}` : '',
      },
    );
    console.log('fcm token removed ==>', response);
  } catch (err) {
    console.log('ERRR==', err);
    sendErrorReport(err, 'remove_token_api_helper');
  }
}
export function getApiData(endpoint, method, data, headers) {
  const authState = store?.getState() || {};
  const { token } = authState?.auth?.accessToken || '';
  const { uuid } = authState?.auth || '';
  const authHeaders = {
    'Content-Type': 'application/json',
    authorization: token ? `Bearer ${token}` : '',
  };

  return new Promise((resolve, reject) => {
    let query = '';
    let qs = '';
    for (const key in data) {
      query += `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}&`;
    }
    const params = {};
    params.method = method.toLowerCase() === 'get' ? 'get' : 'post';
    if (headers) {
      params.headers = headers;
    } else {
      params.headers = authHeaders;
    }
    // console.log(params.headers);
    if (params.method === 'post') {
      if (
        params.headers &&
        params.headers['Content-Type'] &&
        params.headers['Content-Type'] === 'application/json'
      ) {
        params.body = JSON.stringify(data);
      } else {
        params.body = query;
      }
    } else {
      qs = `?${query}`;
    }

    // console.log('params=--', params, endpoint);

    if (
      params.method === 'post' &&
      params.headers &&
      params.headers['Content-Type'] &&
      params.headers['Content-Type'] === 'application/json'
    ) {
      // console.log(JSON.stringify(data));
    } else {
      let str = '';
      if (data && Object.keys(data).length > 0) {
        Object.keys(data).map(dk => {
          str += `${dk}:${data[dk]}\n`;
        });
      }
      console.log(str);
    }
    console.log(
      'BaseSetting.api + endpoint + qs====>>>>',
      BaseSetting.api + endpoint + qs,
      'data====',
      data,
    );
    fetch(BaseSetting.api + endpoint + qs, params)
      .then(response => response.json())
      .then(resposeJson => {
        // console.log('resposeJson===>>>', resposeJson);
        if (resposeJson.status !== 200 && resposeJson.status !== 401) {
          // const datas = {
          //   message: 'Could not find this method',
          //   url: BaseSetting.api + endpoint + qs,
          //   params,
          // };
          // setBugsNagLog(JSON.stringify(datas));
        }

        if (
          isObject(resposeJson) &&
          isString(resposeJson.message) &&
          resposeJson.message === 'Unauthorized' &&
          endpoint !== 'delete-token' &&
          !isEmpty(token) &&
          !isEmpty(uuid)
        ) {
          // console.log('Unauthorized===>>>');
          removeToken(token, uuid);
          store.dispatch(AuthAction.setAccessToken(''));
          store.dispatch(AuthAction.setUserData({}));
          store.dispatch(AuthAction.setUserId(''));
          store.dispatch(AuthAction.setUUid(''));

          navigation.navigate('RedirectLS');
          resolve(resposeJson);
        } else {
          resolve(resposeJson);
        }
      })
      .catch(err => {
        // console.log('ERRR==--->>>>>', err);
        // const datas = {
        //   message: `Api Error in ${endpoint}`,
        //   url: BaseSetting.api + endpoint + qs,
        //   params,
        //   err,
        // };
        // setBugsNagLog(JSON.stringify(datas));
        // Bugsnag.notify(err, report => {
        //   report.metadata = {
        //     data: {
        //       endpoint,
        //       data,
        //     },
        //   };
        // });
        console.log('error_apihelper', err);
        reject(err);
      });
  });
}

export function getApiDataProgress(
  endpoint,
  method,
  data,
  headers,
  onProgress,
) {
  return new Promise((resolve, reject) => {
    const url = BaseSetting.api + endpoint;
    const oReq = new XMLHttpRequest();
    const authState = store?.getState() || {};
    const { token } = authState?.auth?.accessToken || '';
    const authHeaders = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    oReq.upload.addEventListener('progress', event => {
      if (event.lengthComputable) {
        const progress = (event.loaded * 100) / event.total;
        if (onProgress) {
          onProgress(progress);
        }
      } else {
        // Unable to compute progress information since the total size is unknown
      }
    });

    const query = new FormData();
    if (data && Object.keys(data).length > 0) {
      Object.keys(data).map(k => query.append(k, data[k]));
    }
    const params = query;
    oReq.open(method, url, true);

    oReq.setRequestHeader('Content-Type', 'multipart/form-data');
    if (isObject(headers)) {
      Object.keys(headers).map(hK => {
        oReq.setRequestHeader(hK, headers[hK]);
      });
    }

    if (token) {
      oReq.setRequestHeader('Authorization', `Bearer ${token}`);
    }

    oReq.send(params);
    oReq.onreadystatechange = () => {
      if (oReq.readyState === XMLHttpRequest.DONE) {
        try {
          const resposeJson = JSON.parse(oReq.responseText);
          if (
            isObject(resposeJson) &&
            isString(resposeJson.message) &&
            resposeJson.message === 'Unauthorized' &&
            endpoint !== 'delete-token'
          ) {
            resolve(resposeJson);
          } else {
            resolve(resposeJson);
          }
        } catch (exe) {
          // const datas = {
          //   message: `Api Error in ${endpoint}`,
          //   url: endpoint,
          //   params,
          // };
          // setBugsNagLog(JSON.stringify(datas));
          console.log(exe);
          reject(exe);
        }
      }
    };
  });
}
